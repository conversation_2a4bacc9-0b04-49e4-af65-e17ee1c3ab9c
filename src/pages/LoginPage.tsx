import React, { useState } from "react";
import {
	View,
	Text,
	TextInput,
	TouchableOpacity,
	StyleSheet,
} from "react-native";
import { GLOBAL_STYLES } from "../styles/globalStyles";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";

const LoginPage = () => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	const handleLogin = () => {
		navigation.navigate("HomePage");
	};

	return (
		<View style={styles.pageBackground}>
			<View style={styles.card}>
				<Text style={styles.title}>LOG IN</Text>
				<View style={styles.formGroup}>
					<Text style={styles.label}>Email</Text>
					<TextInput
						style={styles.input}
						value={email}
						onChangeText={setEmail}
						placeholder="Enter your email"
						placeholderTextColor={GLOBAL_STYLES.COLORS.TEXT_TERTIARY}
						autoCapitalize="none"
						keyboardType="email-address"
					/>
				</View>
				<View style={styles.formGroup}>
					<Text style={styles.label}>Password</Text>
					<TextInput
						style={styles.input}
						value={password}
						onChangeText={setPassword}
						placeholder="Enter your password"
						placeholderTextColor={GLOBAL_STYLES.COLORS.TEXT_TERTIARY}
						secureTextEntry
					/>
				</View>
				<TouchableOpacity
					style={[styles.button, { opacity: 0.6 }]}
					onPress={handleLogin}
					activeOpacity={1}
				>
					<Text style={styles.buttonText}>Log in</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	pageBackground: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
	},
	card: {
		backgroundColor: "#131c2b",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(64),
		minWidth: scale(700),
		width: "90%",
		maxWidth: scale(900),
		alignItems: "center",
		shadowColor: "#000",
		shadowOffset: { width: 0, height: scale(2) },
		shadowOpacity: 0.2,
		shadowRadius: scale(8),
		elevation: 4,
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(56),
		fontWeight: "bold",
		marginBottom: scale(48),
		letterSpacing: scale(2),
	},
	formGroup: {
		width: "100%",
		marginBottom: scale(36),
	},
	label: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(28),
		marginBottom: scale(10),
		marginLeft: scale(2),
	},
	input: {
		backgroundColor: "#18243a",
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(24),
		paddingHorizontal: scale(28),
		fontSize: scale(28),
		borderWidth: scale(2),
		borderColor: "#22304a",
	},
	button: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(24),
		paddingHorizontal: 0,
		marginTop: scale(24),
		width: "100%",
		alignItems: "center",
	},
	buttonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(32),
		fontWeight: "bold",
		letterSpacing: scale(1),
	},
});

export default LoginPage;
