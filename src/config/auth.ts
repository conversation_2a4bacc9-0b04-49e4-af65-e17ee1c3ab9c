// expired Sunday, September 14, 2025 1:05:24 PM (GMT 0)
export const AUTH_TOKEN =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI2MzIwZWQ2My1jMGQzLTQ3NjMtYjk1YS02ODRlODg5NmFlNzMiLCJpYXQiOjE3NTAwNzYzNzMsImV4cCI6MTc1MDE2Mjc3MywiYXVkIjoiZmFuIiwianRpIjoiZGY4ZTdlMmUtODE4ZS00ZTE3LWJkZjItMTQ5NGQxNzJmNGNlIn0.yAXxIdCi139e4IyPlwNZA3PQnm3SAgHdXN9ztGMFJuw"; // "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI3ZjE4MDQwNC1mNmNlLTQwNTMtYmI4YS1jYjU5MmJhMDQxYmQiLCJpYXQiOjE3NDg0MzgwMzMsImV4cCI6MTc0ODUyNDQzMywiYXVkIjoiZmFuIiwianRpIjoiZWQxMTM0N2QtOWZhYy00YWI4LTkxNDAtOWM3YmZlYTc5MDI2In0.7D90QbjhUXw0WjfhThRGBwEmXxAgHJgeSuvBy1o03zo";
// for production load this from environment variables or a secure configuration service
export const getAuthToken = () => AUTH_TOKEN;
