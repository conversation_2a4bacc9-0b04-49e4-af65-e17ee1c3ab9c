// Log in console details of components from it's codename
// example: codename="home___grid_10_contenus_les_plus_regardes_cette_se"
// title: "Most popular content"

/**
 Component 4:
 LOG    • Type: section_static_carousel
 LOG    • Codename: home___grid_10_contenus_les_plus_regardes_cette_se
 LOG    • Title: Most popular content
 LOG    • Items Count: 10
 LOG    • Item Types: ["video"]
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_1", "_kenticoId": "efa4fbfe-09b5-40ce-ab05-ed8443a64f17", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "2ba7513b-5b54-47c1-8733-104db75611e9", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 {
  "_kenticoCodename": "video_top_10___n_1",
  "_kenticoId": "efa4fbfe-09b5-40ce-ab05-ed8443a64f17",
  "_kenticoItemType": "video",
  "_kenticoLanguage": "en",
  "currentFanViews": 0,
  "description": "",
  "duration": null,
  "fullDescription": null,
  "hasBeenViewed": false,
  "isNew": false,
  "itemId": "2ba7513b-5b54-47c1-8733-104db75611e9",
  "itemType": "video",
  "marker": 0,
  "name": "",
  "poster": null,
  "posterPortrait": null,
  "ratio": "sixteen-nine",
  "technicalDescription": null,
  "urlSlug": null,
  "views": 0
}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_2", "_kenticoId": "377bbbe9-903c-4330-b3b4-0f3b806d4aeb", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "05b91b8e-d68e-4a3b-b305-e4dfa4528cd7", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "f1829ca8_7a87_4b8b_8d52_647c7617e7c4", "_kenticoId": "b454dcf8-28b6-4ac5-8f45-5c223b0da196", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "de51a966-37ba-4ad9-9102-b4c147c936af", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_4", "_kenticoId": "e9bf9cf5-f6c9-440f-8555-3d80b5993c07", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "45ff09dd-7b9e-4e51-a1fd-52b135240520", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_4_1795557", "_kenticoId": "17955578-1faf-48e2-bb9c-f2d3314f7ee0", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "0009f750-e1ab-4ede-b60a-25607db30653", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_6", "_kenticoId": "a4720a96-38e3-4364-9b01-101447ac2a3c", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "5c7e60ff-4a5e-49ad-9beb-04c3700269bf", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_7", "_kenticoId": "251e00d1-0102-445c-888b-e44999e66aaf", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "d213ed2d-ba75-419e-9cc1-8568b5282a3f", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_8", "_kenticoId": "f1a5ebb3-2f17-4510-b984-e3d4792bb101", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "af9fc967-0701-4bf1-933c-d792639e29e2", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_9", "_kenticoId": "1ca123e2-bd6d-404f-867a-786645cf67c3", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "d55d472d-05c0-49d3-a1c5-d0e06a9cdcd6", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG      - Item: {"_kenticoCodename": "video_top_10___n_10", "_kenticoId": "06de5653-a1bf-4d45-ba83-a1af77549096", "_kenticoItemType": "video", "_kenticoLanguage": "en", "currentFanViews": 0, "description": "", "duration": null, "fullDescription": null, "hasBeenViewed": false, "isNew": false, "itemId": "d2509389-283c-410d-bfb8-2488faeb3efa", "itemType": "video", "marker": 0, "name": "", "poster": null, "posterPortrait": null, "ratio": "sixteen-nine", "technicalDescription": null, "urlSlug": null, "views": 0}
 LOG  
 */

import { StyleSheet, Text, View, FlatList } from "react-native";
import React, { useEffect, useState } from "react";
import {
	VideoCard,
	PlaylistCard,
	MostViewedStaticSection,
} from "../../../utils/apis/generated/kentico";

import {
	kenticoAPIClient,
	setAuthToken,
} from "../../../utils/kenticoInstance";
import { AUTH_TOKEN } from "../../../config/auth";

const SuggestedVideos = () => {
	console.log("--------------------------------");
	console.log("[SuggestedVideos] first load");

	const [mostPopularVideos, setMostPopularVideos] = useState<
		VideoCard[]
	>([]);
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		console.log("[SuggestedVideos] useEffect mounted");
		setAuthToken(AUTH_TOKEN);

		const fetchSuggestedVideos = async () => {
			setIsLoading(true);

			try {
				// validation checks
				if (!kenticoAPIClient?.ott?.getStaticSectionByCodename) {
					throw new Error(
						"Kentico API client not properly initialized"
					);
				}

				// Use the home page codename
				const codename = "home"; // or "home___new_slider_4cf7e3c" if that is more appropriate
				const params = { language: "en", previewFlag: false };
				// console.log(
				// 	`[SuggestedVideos] Making API request to getStaticSectionByCodename with codename: ${codename} and params:`,
				// 	params
				// );

				const response =
					await kenticoAPIClient.ott.getStaticSectionByCodename(
						codename,
						params
					);

				// Validate response
				if (!response?.data) {
					console.error("Invalid response format");
				}

				console.log("----------- <Testing Start> ----------------");
				console.log(
					"========================================================================"
				);

				// Type guard to check if components exist and is an array
				if (Array.isArray((response.data as any).components)) {
					const components = (response.data as any).components;
					console.log("\n[Components]");
					console.log("------------");
					components.forEach((component: any, index: number) => {
						console.log(`\nComponent ${index + 1}:`);
						console.log("  • Type:", component._kenticoItemType);
						console.log("  • Codename:", component._kenticoCodename);
						console.log("  • Title:", component.title);

						if (
							component._kenticoCodename ===
							"home___grid_10_contenus_les_plus_regardes_cette_se"
						) {
							console.log("  • Items Count:", component.items.length);
							console.log(
								"  • Item Types:",
								Array.from(
									new Set(
										component.items.map((item: any) => item.itemType)
									)
								)
							);
							component.items.forEach((item: any) => {
								console.log("    - Item:", item);
							});
						}
					});
				}

				console.log(
					"========================================================================"
				);
				console.log("----------- <Finish> ----------------");

				// Type assertion for MostViewedStaticSection
				const mostPopularSection =
					response.data as MostViewedStaticSection;

				// Log the fetched data
				// console.log("====================================");
				// console.log(
				// 	"[SuggestedVideos] Most Viewed Section Data:",
				// 	JSON.stringify(mostPopularSection, null, 2)
				// );

				// #TODO undo commented block
				if (mostPopularSection.items) {
					// Filter out only VideoCard items
					const videosOnly = mostPopularSection.items.filter(
						(item): item is VideoCard => item.itemType === "video"
					);
					setMostPopularVideos(videosOnly);
				}
			} catch (error: any) {
				console.error("[SuggestedVideos] Error details:", {
					errorType: error.name,
					errorMessage: error.message,
					stackTrace: error.stack,
				});

				setError(error.message);
			} finally {
				setIsLoading(false);
			}
		};
		fetchSuggestedVideos();

		// Cleanup function (optional)
		return () => {
			console.log("[SuggestedVideos] useEffect unmounted");
		};
	}, []); // Empty dependency array to run only on mount

	// Show loading state while fetching
	if (isLoading) {
		return (
			<View style={styles.container}>
				<Text style={styles.text}>Loading videos...</Text>
			</View>
		);
	}

	// Show error state if there was an error
	if (error) {
		return (
			<View style={styles.container}>
				<Text style={styles.text}>Error loading videos</Text>
				<Text style={[styles.text, { color: "#ff4444" }]}>
					{error}
				</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text style={styles.text}>Most Popular Videos</Text>
			<FlatList
				data={mostPopularVideos}
				keyExtractor={(item) => item.itemId}
				renderItem={({ item }) => (
					<View style={styles.videoItem}>
						<Text style={styles.videoTitle}>{item.name}</Text>
						<Text style={styles.videoDescription}>
							{item.description}
						</Text>
					</View>
				)}
			/>
		</View>
	);
};

export default SuggestedVideos;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		backgroundColor: "#262626",
	},
	text: {
		color: "#fff",
		fontSize: 24,
		fontWeight: "bold",
		marginBottom: 16,
	},
	videoItem: {
		padding: 16,
		borderBottomWidth: 1,
		borderBottomColor: "#444",
	},
	videoTitle: {
		color: "#fff",
		fontSize: 18,
		fontWeight: "600",
	},
	videoDescription: {
		color: "#ccc",
		fontSize: 14,
		marginTop: 8,
	},
});
