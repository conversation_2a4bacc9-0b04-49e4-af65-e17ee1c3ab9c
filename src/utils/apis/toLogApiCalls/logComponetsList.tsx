// Log in console list of components from codename="home"
/**
  LOG  
[Components]
 LOG  ------------
 LOG  
Component 1:
 LOG    • Type: section_static_carousel
 LOG    • Codename: home___new_slider_4cf7e3c
 LOG    • Title: In the news
 LOG    • Items Count: 4
 LOG    • Item Types: ["video"]
 LOG  
Component 2:
 LOG    • Type: section_static_carousel
 LOG    • Codename: grid_home_page___competitions
 LOG    • Title: 
 LOG    • Items Count: 14
 LOG    • Item Types: [undefined]
 LOG  
Component 3:
 LOG    • Type: section_dynamic_live
 LOG    • Codename: page_home___lives_a_venir
 LOG    • Title: Coming lives
 LOG  
Component 4:
 LOG    • Type: section_static_carousel
 LOG    • Codename: home___grid_10_contenus_les_plus_regardes_cette_se
 LOG    • Title: Most popular content
 LOG    • Items Count: 10
 LOG    • Item Types: ["video"]
 LOG  
Component 5:
 LOG    • Type: section_static_carousel
 LOG    • Codename: home_page___grid_categories
 LOG    • Title: Catégories
 LOG    • Items Count: 7
 LOG    • Item Types: ["category"]
 LOG  
Component 6:
 LOG    • Type: section_static_ad
 LOG    • Codename: bein_sports___static_ad
 LOG    • Title: undefined
 LOG  
Component 7:
 LOG    • Type: section_dynamic_carousel
 LOG    • Codename: home___grid_lfh_ddba51e
 LOG    • Title: undefined
 LOG  
Component 8:
 LOG    • Type: section_static_carousel
 LOG    • Codename: home___grid_lnh_5a5c0bc
 LOG    • Title: The best of the opening days of Liqui Moly StarLigue and Proligue
 LOG    • Items Count: 8
 LOG    • Item Types: ["video"]
 LOG  
Component 9:
 LOG    • Type: section_dynamic_carousel
 LOG    • Codename: home___grid_ffhandball
 LOG    • Title: undefined
   */

import { StyleSheet, Text, View, FlatList } from "react-native";
import React, { useEffect, useState } from "react";
import {
	VideoCard,
	PlaylistCard,
	MostViewedStaticSection,
} from "../../../utils/apis/generated/kentico";

import {
	kenticoAPIClient,
	setAuthToken,
} from "../../../utils/kenticoInstance";
import { AUTH_TOKEN } from "../../../config/auth";

const SuggestedVideos = () => {
	console.log("--------------------------------");
	console.log("[SuggestedVideos] first load");

	const [mostPopularVideos, setMostPopularVideos] = useState<
		VideoCard[]
	>([]);
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		console.log("[SuggestedVideos] useEffect mounted");
		setAuthToken(AUTH_TOKEN);

		const fetchSuggestedVideos = async () => {
			setIsLoading(true);

			try {
				// validation checks
				if (!kenticoAPIClient?.ott?.getStaticSectionByCodename) {
					throw new Error(
						"Kentico API client not properly initialized"
					);
				}

				// Use the home page codename
				const codename = "home"; // or "home___new_slider_4cf7e3c" if that is more appropriate
				const params = { language: "en", previewFlag: false };
				// console.log(
				// 	`[SuggestedVideos] Making API request to getStaticSectionByCodename with codename: ${codename} and params:`,
				// 	params
				// );

				const response =
					await kenticoAPIClient.ott.getStaticSectionByCodename(
						codename,
						params
					);

				// Validate response
				if (!response?.data) {
					console.error("Invalid response format");
				}

				console.log("----------- <Testing Start> ----------------");
				console.log(
					"========================================================================"
				);

				// Type guard to check if components exist and is an array
				if (Array.isArray((response.data as any).components)) {
					const components = (response.data as any).components;
					console.log("\n[Components]");
					console.log("------------");
					components.forEach((component: any, index: number) => {
						console.log(`\nComponent ${index + 1}:`);
						console.log("  • Type:", component._kenticoItemType);
						console.log("  • Codename:", component._kenticoCodename);
						console.log("  • Title:", component.title);

						if (component.items) {
							console.log("  • Items Count:", component.items.length);
							console.log(
								"  • Item Types:",
								Array.from(
									new Set(
										component.items.map((item: any) => item.itemType)
									)
								)
							);
						}
					});
				}
				console.log(
					"========================================================================"
				);
				console.log("----------- <Finish> ----------------");

				// Type assertion for MostViewedStaticSection
				const mostPopularSection =
					response.data as MostViewedStaticSection;

				// Log the fetched data
				// console.log("====================================");
				// console.log(
				// 	"[SuggestedVideos] Most Viewed Section Data:",
				// 	JSON.stringify(mostPopularSection, null, 2)
				// );

				// #TODO undo commented block
				if (mostPopularSection.items) {
					// Filter out only VideoCard items
					const videosOnly = mostPopularSection.items.filter(
						(item): item is VideoCard => item.itemType === "video"
					);
					setMostPopularVideos(videosOnly);
				}
			} catch (error: any) {
				console.error("[SuggestedVideos] Error details:", {
					errorType: error.name,
					errorMessage: error.message,
					stackTrace: error.stack,
				});

				setError(error.message);
			} finally {
				setIsLoading(false);
			}
		};
		fetchSuggestedVideos();

		// Cleanup function (optional)
		return () => {
			console.log("[SuggestedVideos] useEffect unmounted");
		};
	}, []); // Empty dependency array to run only on mount

	// Show loading state while fetching
	if (isLoading) {
		return (
			<View style={styles.container}>
				<Text style={styles.text}>Loading videos...</Text>
			</View>
		);
	}

	// Show error state if there was an error
	if (error) {
		return (
			<View style={styles.container}>
				<Text style={styles.text}>Error loading videos</Text>
				<Text style={[styles.text, { color: "#ff4444" }]}>
					{error}
				</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text style={styles.text}>Most Popular Videos</Text>
			<FlatList
				data={mostPopularVideos}
				keyExtractor={(item) => item.itemId}
				renderItem={({ item }) => (
					<View style={styles.videoItem}>
						<Text style={styles.videoTitle}>{item.name}</Text>
						<Text style={styles.videoDescription}>
							{item.description}
						</Text>
					</View>
				)}
			/>
		</View>
	);
};

export default SuggestedVideos;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		backgroundColor: "#262626",
	},
	text: {
		color: "#fff",
		fontSize: 24,
		fontWeight: "bold",
		marginBottom: 16,
	},
	videoItem: {
		padding: 16,
		borderBottomWidth: 1,
		borderBottomColor: "#444",
	},
	videoTitle: {
		color: "#fff",
		fontSize: 18,
		fontWeight: "600",
	},
	videoDescription: {
		color: "#ccc",
		fontSize: 14,
		marginTop: 8,
	},
});
