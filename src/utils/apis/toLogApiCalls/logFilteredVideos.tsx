/**
  LOG  
[Filtered Videos]
 LOG  --------------
 LOG  Total Videos Found: 10
 LOG  
Video 1:
 LOG    • ID: 2ba7513b-5b54-47c1-8733-104db75611e9
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 2:
 LOG    • ID: 05b91b8e-d68e-4a3b-b305-e4dfa4528cd7
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 3:
 LOG    • ID: de51a966-37ba-4ad9-9102-b4c147c936af
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 4:
 LOG    • ID: 45ff09dd-7b9e-4e51-a1fd-52b135240520
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 5:
 LOG    • ID: 0009f750-e1ab-4ede-b60a-25607db30653
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 6:
 LOG    • ID: 5c7e60ff-4a5e-49ad-9beb-04c3700269bf
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 7:
 LOG    • ID: d213ed2d-ba75-419e-9cc1-8568b5282a3f
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 8:
 LOG    • ID: af9fc967-0701-4bf1-933c-d792639e29e2
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 9:
 LOG    • ID: d55d472d-05c0-49d3-a1c5-d0e06a9cdcd6
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  
Video 10:
 LOG    • ID: d2509389-283c-410d-bfb8-2488faeb3efa
 LOG    • Name: 
 LOG    • Description: 
 LOG    • Ratio: sixteen-nine
 LOG  --------------------------------
 */

import { StyleSheet, Text, View, FlatList } from "react-native";
import React, { useEffect, useState } from "react";
import {
	VideoCard,
	PlaylistCard,
	MostViewedStaticSection,
} from "../../../utils/apis/generated/kentico";

import {
	kenticoAPIClient,
	setAuthToken,
} from "../../../utils/kenticoInstance";
import { AUTH_TOKEN } from "../../../config/auth";

const SuggestedVideos = () => {
	console.log("--------------------------------");
	console.log("[SuggestedVideos] first load");

	const [mostPopularVideos, setMostPopularVideos] = useState<
		VideoCard[]
	>([]);
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		console.log("[SuggestedVideos] useEffect mounted");
		setAuthToken(AUTH_TOKEN);

		const fetchSuggestedVideos = async () => {
			setIsLoading(true);

			try {
				// validation checks
				if (!kenticoAPIClient?.ott?.getStaticSectionByCodename) {
					throw new Error(
						"Kentico API client not properly initialized"
					);
				}

				// Use the home page codename
				const codename = "home"; // or "home___new_slider_4cf7e3c" if that is more appropriate
				const params = { language: "en", previewFlag: false };
				// console.log(
				// 	`[SuggestedVideos] Making API request to getStaticSectionByCodename with codename: ${codename} and params:`,
				// 	params
				// );

				const response =
					await kenticoAPIClient.ott.getStaticSectionByCodename(
						codename,
						params
					);

				// Validate response
				if (!response?.data) {
					console.error("Invalid response format");
				}

				console.log("----------- <Testing Start> ----------------");
				console.log(
					"========================================================================"
				);

				// Type guard to check if components exist and is an array
				if (Array.isArray((response.data as any).components)) {
					const components = (response.data as any).components;
					console.log("\n[Components]");
					console.log("------------");
					components.forEach((component: any, index: number) => {
						console.log(`\nComponent ${index + 1}:`);
						console.log("  • Type:", component._kenticoItemType);
						console.log("  • Codename:", component._kenticoCodename);
						console.log("  • Title:", component.title);

						if (
							component._kenticoCodename ===
							"home___grid_10_contenus_les_plus_regardes_cette_se"
						) {
							console.log("  • Items Count:", component.items.length);
							console.log(
								"  • Item Types:",
								Array.from(
									new Set(
										component.items.map((item: any) => item.itemType)
									)
								)
							);
							component.items.forEach((item: any) => {
								console.log("    - Item:", item);
							});

							// Assign the most popular section
							const mostPopularSection = component;

							// Log the filtered videos
							if (mostPopularSection.items) {
								const videosOnly = mostPopularSection.items.filter(
									(item): item is VideoCard =>
										item.itemType === "video"
								);

								console.log("\n[Filtered Videos]");
								console.log("--------------");
								console.log("Total Videos Found:", videosOnly.length);
								videosOnly.forEach((video, index) => {
									console.log(`\nVideo ${index + 1}:`);
									console.log("  • ID:", video.itemId);
									console.log("  • Name:", video.name);
									console.log("  • Description:", video.description);
									console.log("  • Ratio:", video.ratio);
								});

								setMostPopularVideos(videosOnly);
							}
						}
					});
				}
				console.log(
					"========================================================================"
				);
				console.log("----------- <Finish> ----------------");
			} catch (error: any) {
				console.error("[SuggestedVideos] Error details:", {
					errorType: error.name,
					errorMessage: error.message,
					stackTrace: error.stack,
				});

				setError(error.message);
			} finally {
				setIsLoading(false);
			}
		};
		fetchSuggestedVideos();

		// Cleanup function (optional)
		return () => {
			console.log("[SuggestedVideos] useEffect unmounted");
		};
	}, []); // Empty dependency array to run only on mount

	// Show loading state while fetching
	if (isLoading) {
		return (
			<View style={styles.container}>
				<Text style={styles.text}>Loading videos...</Text>
			</View>
		);
	}

	// Show error state if there was an error
	if (error) {
		return (
			<View style={styles.container}>
				<Text style={styles.text}>Error loading videos</Text>
				<Text style={[styles.text, { color: "#ff4444" }]}>
					{error}
				</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text style={styles.text}>Most Popular Videos</Text>
			<FlatList
				data={mostPopularVideos}
				keyExtractor={(item) => item.itemId}
				renderItem={({ item }) => (
					<View style={styles.videoItem}>
						<Text style={styles.videoTitle}>{item.name}</Text>
						<Text style={styles.videoDescription}>
							{item.description}
						</Text>
					</View>
				)}
			/>
		</View>
	);
};

export default SuggestedVideos;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		backgroundColor: "#262626",
	},
	text: {
		color: "#fff",
		fontSize: 24,
		fontWeight: "bold",
		marginBottom: 16,
	},
	videoItem: {
		padding: 16,
		borderBottomWidth: 1,
		borderBottomColor: "#444",
	},
	videoTitle: {
		color: "#fff",
		fontSize: 18,
		fontWeight: "600",
	},
	videoDescription: {
		color: "#ccc",
		fontSize: 14,
		marginTop: 8,
	},
});
