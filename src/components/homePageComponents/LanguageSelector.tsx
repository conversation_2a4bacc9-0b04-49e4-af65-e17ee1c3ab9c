import React from "react";
import {
	View,
	TouchableOpacity,
	Text,
	StyleSheet,
} from "react-native";

interface LanguageSelectorProps {
	currentLanguage: string;
	onLanguageChange: (language: string) => void;
	style?: any;
}

const LanguageSelector = ({
	currentLanguage,
	onLanguageChange,
}: LanguageSelectorProps) => {
	return (
		<View style={styles.container}>
			<TouchableOpacity
				style={[
					styles.button,
					currentLanguage === "en" && styles.activeButton,
				]}
				onPress={() => onLanguageChange("en")}
				// hasTVPreferredFocus={true}
			>
				<Text
					style={[
						styles.text,
						currentLanguage === "en" && styles.activeText,
					]}
				>
					EN
				</Text>
			</TouchableOpacity>
			<TouchableOpacity
				style={[
					styles.button,
					currentLanguage === "fr" && styles.activeButton,
				]}
				onPress={() => onLanguageChange("fr")}
			>
				<Text
					style={[
						styles.text,
						currentLanguage === "fr" && styles.activeText,
					]}
				>
					FR
				</Text>
			</TouchableOpacity>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flexDirection: "row",
		justifyContent: "flex-end",
		padding: 10,
	},
	button: {
		padding: 8,
		marginHorizontal: 5,
		borderRadius: 4,
		borderWidth: 1,
		borderColor: "#fff",
	},
	activeButton: {
		backgroundColor: "#fff",
	},
	text: {
		color: "#fff",
		fontSize: 14,
	},
	activeText: {
		color: "#000",
	},
});

export default LanguageSelector;
